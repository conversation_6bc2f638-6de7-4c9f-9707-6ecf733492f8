<?php

namespace Tests\Unit\Controller;

use App\Http\Controllers\CalendarController;
use App\Models\Field;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(CalendarController::class)]
class CalendarControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create(['role' => 'employee']);
        $this->field = Field::factory()->active()->create([
            'opening_time' => '08:00',
            'closing_time' => '22:00',
            'hourly_rate' => 50.00,
        ]);
    }

    #[Test]
    public function check_availability_requires_parameters()
    {
        $this->actingAs($this->user);
        $controller = new CalendarController;
        $request = Request::create('/calendar/check', 'POST', []);
        $response = $controller->checkAvailability($request);

        $this->assertSame(200, $response->getStatusCode());
        $this->assertFalse($response->getData(true)['available']);
        $this->assertEquals('Missing required parameters', $response->getData(true)['message']);
    }

    #[Test]
    public function check_availability_rejects_nonexistent_field()
    {
        $this->actingAs($this->user);
        $controller = new CalendarController;
        $request = Request::create('/calendar/check', 'POST', [
            'field_id' => 999,
            'date' => Carbon::now()->addDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration' => 2,
        ]);
        $response = $controller->checkAvailability($request);

        $this->assertFalse($response->getData(true)['available']);
        $this->assertEquals('Field not found', $response->getData(true)['message']);
    }

    #[Test]
    public function check_availability_enforces_business_hours_and_past_date()
    {
        $this->actingAs($this->user);
        $controller = new CalendarController;

        // Outside business hours
        $req1 = Request::create('/calendar/check', 'POST', [
            'field_id' => $this->field->id,
            'date' => Carbon::now()->addDay()->format('Y-m-d'),
            'start_time' => '07:00',
            'duration' => 2,
        ]);
        $resp1 = $controller->checkAvailability($req1);
        $this->assertFalse($resp1->getData(true)['available']);
        $this->assertStringContainsString('Bookings must be between', $resp1->getData(true)['message']);

        // Past date
        $req2 = Request::create('/calendar/check', 'POST', [
            'field_id' => $this->field->id,
            'date' => Carbon::now()->subDay()->format('Y-m-d'),
            'start_time' => '10:00',
            'duration' => 2,
        ]);
        $resp2 = $controller->checkAvailability($req2);
        $this->assertFalse($resp2->getData(true)['available']);
        $this->assertEquals('Cannot book in the past', $resp2->getData(true)['message']);
    }
}
