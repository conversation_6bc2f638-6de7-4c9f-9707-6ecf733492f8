<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(\App\Http\Controllers\ReservationController::class)]
class ReservationEditCostDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Field $field;

    protected Utility $utility;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['role' => 'user']);
        $this->field = Field::factory()->active()->create([
            'hourly_rate' => 50.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);
        $this->utility = Utility::factory()->active()->create([
            'hourly_rate' => 10.00,
        ]);
    }

    #[Test]
    public function edit_page_loads_with_existing_reservation_data()
    {
        // Arrange - Create reservation more than 24 hours in the future to be modifiable
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3), // 3 days in future to ensure it's modifiable
            'start_time' => '10:00',
            'end_time' => '12:00',
            'duration_hours' => 2,
            'total_cost' => 100.00,
            'status' => 'Confirmed',
        ]);

        // Add utility to reservation
        $reservation->utilities()->attach($this->utility->id, [
            'hours' => 2,
            'rate' => $this->utility->hourly_rate,
            'cost' => $this->utility->hourly_rate * 2,
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that the page contains the necessary JavaScript variables
        $response->assertSee('originalValues');
        $response->assertSee('existingUtilities');
        $response->assertSee('calculateCost');

        // Check that original values are properly set in JavaScript object
        $response->assertSee("field_id: '{$reservation->field_id}'", false);
        $response->assertSee("start_time: '{$reservation->start_time}'", false);
        $response->assertSee("duration_hours: '{$reservation->duration_hours}'", false);

        // Check that existing utilities data is present
        $response->assertSee($this->utility->name);
        $response->assertSee($this->utility->hourly_rate);

        // Check that cost display elements are present (don't escape HTML)
        $response->assertSee('id="costDisplay"', false);
        $response->assertSee('id="totalCost"', false);
        $response->assertSee('id="fieldCost"', false);
        $response->assertSee('id="utilityCost"', false);
    }

    #[Test]
    public function cost_estimation_endpoint_works_with_utilities()
    {
        // Arrange
        $this->actingAs($this->user);

        // Act
        $response = $this->postJson(route('reservations.cost-estimate'), [
            'field_id' => $this->field->id,
            'duration_hours' => 2,
            'start_time' => '10:00',
            'utilities' => [
                [
                    'id' => $this->utility->id,
                    'hours' => 2,
                ],
            ],
        ]);

        // Assert
        $response->assertOk();
        $response->assertJsonStructure([
            'total_cost',
            'field_cost',
            'utility_breakdown' => [
                '*' => [
                    'utility_id',
                    'name',
                    'hours',
                    'rate',
                    'cost',
                ],
            ],
        ]);

        $data = $response->json();
        $this->assertGreaterThan(0, $data['total_cost']);
        $this->assertEquals(100.00, $data['field_cost']); // 50 * 2 hours
        $this->assertCount(1, $data['utility_breakdown']);
        $this->assertEquals(20.00, $data['utility_breakdown'][0]['cost']); // 10 * 2 quantity (not multiplied by field duration)
    }

    #[Test]
    public function edit_page_contains_fallback_cost_calculation_logic()
    {
        // Arrange - Create reservation more than 24 hours in the future to be modifiable
        $reservation = Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->addDays(3), // 3 days in future to ensure it's modifiable
            'start_time' => '14:00',
            'end_time' => '16:00',
            'duration_hours' => 2,
            'total_cost' => 100.00,
            'status' => 'Confirmed',
        ]);

        $this->actingAs($this->user);

        // Act
        $response = $this->get(route('reservations.edit', $reservation));

        // Assert
        $response->assertOk();

        // Check that the fallback logic is present in the JavaScript
        $response->assertSee('originalValues.start_time');
        $response->assertSee('Using original start time for cost calculation');
        $response->assertSee('Fallback: Triggering cost calculation after timeout');

        // Check that updateTimeSlots calls calculateCost
        $response->assertSee('calculateCost();', false); // Don't escape HTML

        // Check that the timeout fallback is present
        $response->assertSee('setTimeout(function()');
        $response->assertSee('2000'); // 2 second timeout
    }
}
